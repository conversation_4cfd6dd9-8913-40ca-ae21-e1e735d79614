"""Trading Engine module for coordinating order and position management."""

import os
import json
import logging
import copy
from datetime import date
from typing import List, Dict, Callable
from mattlibrary.trading.child_order import <PERSON><PERSON><PERSON><PERSON>
from mattlibrary.trading.child_order_status import <PERSON><PERSON><PERSON>r<PERSON>tatus
from mattlibrary.trading.fx_converter import Fx_Converter
from mattlibrary.trading.symbol_database import SymbolDatabase
from mattlibrary.trading.trade import Trade
from mattlibrary.trading.position import Position
from mattlibrary.trading.parent_order_base import ParentOrderBase
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.execution_plugin_base import ExecutionPluginBase
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.trading.symbol import Symbol
from mattlibrary.trading.execution_data_engines.simulated_execution_engine import SimulatedExecutionEngine


class TradingEngine:
    """
    Central trading engine for executing trading strategies.
    
    The TradingEngine coordinates between market data feeds, trading strategies,
    order execution, and position management. It supports:
    
    - Multiple concurrent trading strategies
    - Real-time market data processing
    - Multi-asset portfolio management
    - Performance tracking and analytics
    
    Args:
        symbol_dict: Mapping of symbol IDs to symbol information
        fx_converter: Foreign exchange rate converter
        execution_plugin_type: Type of execution plugin to use
        track_performance: Whether to track detailed performance metrics
        
    Example:
        >>> engine = TradingEngine(
        ...     symbol_dict=symbols,
        ...     fx_converter=fx_conv,
        ...     execution_plugin_type="SimulatedExecutionEngine",
        ...     track_performance=True
        ... )
        >>> engine.add_strategy(my_strategy)
        >>> engine.process_market_data(market_data)
    """

    def __init__(self, client_id: str, starting_balance: int, base_currency: str, track_performance: bool, execution_plugin_type: str, 
                 static_data_directory: str, read_positions_from_disk: bool, write_positions_to_disk: bool):
        """Initialize the Trading Engine.
        
        Args:
            starting_balance: Initial capital amount for performance tracking
            base_currency: Base currency for P&L calculations (e.g., 'USD', 'EUR')
            track_performance: Whether to track detailed performance metrics
        """
        self.logger = logging.getLogger(__name__)
        
        # Store initialization parameters
        self.client_id = client_id
        self.starting_balance = starting_balance
        self.base_currency = base_currency
        self.track_performance = track_performance
        self.static_data_directory = static_data_directory
        self.read_positions_from_disk = read_positions_from_disk
        self.write_positions_to_disk = write_positions_to_disk
        self.execution_engine: ExecutionPluginBase = None
        
        # Initialize execution engine with callback to handle fills
        if execution_plugin_type == "SimulatedExecutionEngine":
            self.execution_engine = SimulatedExecutionEngine()
            self.execution_engine.register_fill_callback(self.child_order_from_execution_engine)
        else:
            raise ValueError(f"Invalid execution plugin type: {execution_plugin_type}")

        # Initialize strategies dictionary
        self.strategies = dict() # strategy_id -> StrategyBase
        
        # Initialize parent orders dictionary
        self.working_parent_orders: Dict[str, ParentOrderBase] = dict() # parent_order_id -> ParentOrderBase
        
        # Initialize position tracking (from PMS)
        self.fx_converter = Fx_Converter(base_currency, self.static_data_directory)
        self.symbol_database = SymbolDatabase(self.static_data_directory)
        self.symbol_dict = self.symbol_database.get_symbol_dictionary()
        
        # Load positions from disk if requested or create empty positions
        self.current_positions: Dict[str, Dict[str, Position]] = dict() # symbol_id -> {strategy_id -> Position}
        if self.read_positions_from_disk:
            self.load_positions_from_disk()
                    
        # Variables and Collections for tracking (from PMS)
        self.completed_parent_orders: List[ParentOrderBase] = []
        self.trades: List[Trade] = []
        self.position_records: List[Position] = []
        
        self.logger.info(f"Initialized Trading Engine [base_currency={base_currency}, starting_balance={starting_balance} {base_currency}, track_performance={track_performance}, execution_plugin_type={execution_plugin_type}, static_data_directory={static_data_directory}]")


    def add_strategy(self, strategy: StrategyBase):
        """Add a strategy to the trading engine.
        
        Args:
            strategy: The strategy to add, must inherit from StrategyBase
        """
        # Check for duplicate strategy IDs
        if strategy.strategy_id in self.strategies:
            self.logger.error(f"Duplicate strategy ID: {strategy.strategy_id}")
            raise ValueError(f"Strategy with strategy_id {strategy.strategy_id} already exists.")

        # Create positions from strategy and add to current_positions
        strategy_positions = {}

        for symbol_id in strategy.symbols:
            #create new position
            position = Position(strategy.strategy_id, symbol_id)
            
            #add position to strategy positions and current positions            
            strategy_positions[symbol_id] = position
            if symbol_id not in self.current_positions:
                self.current_positions[symbol_id] = {}
            self.current_positions[symbol_id][strategy.strategy_id] = position

        # Configure the strategy with callbacks and positions
        strategy.configure_strategy(self.submit_parent_order, strategy_positions)
        self.strategies[strategy.strategy_id] = strategy
        self.logger.info(f"Added strategy [id={strategy.strategy_id}, description={strategy.description}]")


    def remove_strategy(self, strategy: StrategyBase):
        """Remove a strategy from the trading engine.
        
        Args:
            strategy: The strategy to remove
        """
        if strategy.strategy_id not in self.strategies:
            self.logger.error(f"Strategy not found: {strategy.strategy_id}")
            raise ValueError(f"Strategy with strategy_id {strategy.strategy_id} does not exist.")

        # Remove order submission callback from strategy
        strategy.remove_order_submission_callback()
        
        # Remove the strategy
        del self.strategies[strategy.strategy_id]
        
        self.logger.info(f"Removed strategy [id={strategy.strategy_id}]")

    
    def load_positions_from_disk(self):
        """Load the current positions from disk
        """
        path_filename = os.path.join(self.static_data_directory, f"positions_{self.client_id}.json")

        #check if file exists
        if not os.path.exists(path_filename):
            self.logger.error(f"Could not load positions from disk as file not found: {path_filename}")
            return
        
        #load the positions list from a json file
        with open(path_filename, "r") as f:
            positions = json.load(f)

        for position in positions:
            if position["symbol_id"] not in self.current_positions:
                self.current_positions[position["symbol_id"]] = {}
            self.current_positions[position["symbol_id"]][position["strategy_id"]] = Position.from_dict(position)

        self.logger.info(f"{len(positions)} Positions loaded from disk: {path_filename}")


    def save_positions_to_disk(self):
        """Save the current positions to disk
        """
        path_filename = os.path.join(self.static_data_directory, f"positions_{self.client_id}.json")
        
        position_list = []
        for symbol_positions in self.current_positions.values():
            for pos in symbol_positions.values():
                position_list.append(pos.to_dict())

        #store the positions list in a json file (indent=4 for readability)
        with open(path_filename, "w") as f:
            json.dump(position_list, f, indent=4)
        
        self.logger.info(f"{len(position_list)} Positions saved to disk: {path_filename}")
            

    def remove_all_positions(self):
        """Remove all positions and optionally write empty positions to disk
        """
        self.current_positions = dict()
        if self.write_positions_to_disk:
            self.save_positions_to_disk()
            self.logger.info(f"Positions removed and written to disk: {os.path.join(self.static_data_directory, f'positions_{self.client_id}.json')}")


    def submit_parent_order(self, parent_order: ParentOrderBase):
        """Submit a parent order to the Trading Engine and register callback for child order submission to execution engine."""        
       
        # Create position if it does not yet exist (if no strategies subscribed positions don't automatically get created)        
        for symbol_id in parent_order.symbol_ids:
            if symbol_id not in self.current_positions:
                self.current_positions[symbol_id] = {}
            if parent_order.strategy_id not in self.current_positions[symbol_id]:
                self.current_positions[symbol_id][parent_order.strategy_id] = Position(parent_order.strategy_id, symbol_id)
       
        # Store the parent order
        self.working_parent_orders[parent_order.order_id] = parent_order

        # Register callback for child order submission to execution engine
        parent_order.register_child_order_submission_callback(self.execution_engine.submit_child_order)

        self.logger.info(f"Parent order submitted: Parent Order ID:{parent_order.order_id} - Symbols:{parent_order.symbol_ids}")


    def child_order_from_execution_engine(self, child_order: ChildOrder):
        """Handle child order from the execution engine.
        
        Args:
            child_order: filled, canceled, or rejected child order
        """

        # Update position with the fill (only if the child order is filled)
        if child_order.status == ChildOrderStatus.FILLED:
            self.update_position_with_childorder_fill(child_order)

            #write current_positions to disk if requested
            if self.write_positions_to_disk:
                self.save_positions_to_disk()

        # Find the parent order that generated this child order
        parent_order_id = child_order.parent_order_id
        if parent_order_id not in self.working_parent_orders:
            self.logger.error(f"Parent order not found for child order: {child_order.order_id}")
            raise ValueError(f"Parent order not found for child order: {child_order.order_id}")
        
        # Process the fill in the parent order
        parent_order = self.working_parent_orders[parent_order_id]
        parent_order.process_fill(child_order)

        # remove parent order if completed
        if parent_order.order_status == ParentOrderStatus.COMPLETED:
            #check if parent order is still in working parent orders
            if parent_order_id in self.working_parent_orders:
                
                #notify strategy of its parent order completion
                if parent_order.strategy_id in self.strategies:
                    self.strategies[parent_order.strategy_id].parent_order_completed_notification(parent_order)
                
                #remove parent order from working parent orders
                del self.working_parent_orders[parent_order_id]
                # Record the parent order in performance metrics if tracking is enabled
                if self.track_performance:
                    self.completed_parent_orders.append(parent_order)

                # Log completion
                self.logger.info(f"Parent order completed: {parent_order.order_id}")
            

    def process_market_data(self, row:dict):
        """Process current market data, update positions, and run strategies.
        
        Args:
            row: A data row (dictionary or named tuple) containing market data (symbol, datetime, bid/ask or close)
        """

        # Extract data from the row as function of the type of data
        symbol_id = row["symbol"]
        time_stamp = row["datetime"]

        if "bid" in row and "ask" in row:
            bid_price = row["bid"]
            ask_price = row["ask"]
        else:
            bid_price = row["close"]
            ask_price = row["close"]
        
        # 1. Feed market data to execution engine for order processing (if simulated)
        # Updates the execution engine with market data (if simulated) and executes any pending orders if conditions are met
        self.execution_engine.process_market_data(symbol_id, time_stamp, bid_price, ask_price)

        # 2. Process data through each strategy (if any strategies were added)
        # Strategies may submit parentorders based on the market data -> working_parent_orders collection (resting)
        for strategy in self.strategies.values():
            if symbol_id in strategy.symbols:
                strategy.process_data(row)
        
        # 3. Pass market data to each parent order (if symbol matches)
        # Parent orders may submit child orders based on the market data -> execution engine
        # Execution engine may fill child orders -> updates matching position and parent order
        # Can cause other child orders from being submitted by parent order and getting filled 
        for parent_order_id in list(self.working_parent_orders.keys()):
            parent_order = self.working_parent_orders[parent_order_id]
            if symbol_id in parent_order.symbol_ids:
                parent_order.process_market_data(symbol_id, time_stamp, bid_price, ask_price)

        # 4. Update positions with market data
        # Updates matching positions with new market data 
        for position in self.current_positions[symbol_id].values():
            
            # Update position with current timestamp and price
            position.timestamp_current = time_stamp
            position.current_price = (bid_price + ask_price) / 2

            # Calculate unrealized P&L
            if position.size == 0:
                # No position, no unrealized P&L
                position.unrealized_pnl_local = 0.0
                position.unrealized_pnl_base = 0.0
            else:
                # Calculate unrealized P&L in local currency
                position.unrealized_pnl_local = (position.current_price - position.average_price) * position.size
                # Convert to base currency
                position.unrealized_pnl_base = position.unrealized_pnl_local * \
                    self.fx_converter.get_fx_conversion_rate(time_stamp, self.symbol_dict[symbol_id].currency)

        # 5. Record positions (copy) for performance tracking
        if self.track_performance:
            for position in self.current_positions[symbol_id].values():
                self.position_records.append(copy.copy(position))


    def finalize(self):
        """Finalize the Trading Engine."""
        # Finalize each strategy
        for strategy in self.strategies.values():
            strategy.finalize()
        
        self.logger.info(f"Trading Engine finalized [strategies={len(self.strategies)}]")


    def update_position_with_childorder_fill(self, child_order: ChildOrder):
        """Update positions with the current child order fill.
        
        Args:
            child_order: The filled order
        """
        
        # Get or create position
        position = self.current_positions[child_order.symbol_id][child_order.strategy_id]
        
        # Update position timestamp and current price
        position.timestamp_current = child_order.timestamp_filled
        position.current_price = child_order.filled_price

        # Store current position details for P&L calculations
        current_size = position.size
        prev_avg_price = position.average_price

        # Calculate new position size 
        new_size = current_size + child_order.filled_size
        
        # Get FX conversion rate for P&L calculations in base currency
        fx_rate = self.fx_converter.get_fx_conversion_rate(
            child_order.timestamp_filled, 
            self.symbol_dict[child_order.symbol_id].currency
        )

        # Case 1: Position increasing in same direction (adding to long or adding to short)
        if (current_size >= 0 and child_order.filled_size > 0) or (current_size <= 0 and child_order.filled_size < 0):
            if current_size == 0:
                # New position - set average price to current price
                position.average_price = child_order.filled_price
                position.timestamp_first_fill = child_order.timestamp_filled
            else:
                # Adding to existing position - calculate new average price weighted by position sizes
                position.average_price = (abs(child_order.filled_size * child_order.filled_price) + abs(current_size * prev_avg_price)) / (abs(child_order.filled_size) + abs(current_size))
            position.size = new_size
            return

        # Case 2: Partial position reduction (reducing long or reducing short, but not crossing zero)
        if abs(current_size) > abs(child_order.filled_size):
            # Calculate realized P&L for the portion being closed
            realized_pnl_local = (child_order.filled_price - prev_avg_price) * abs(child_order.filled_size) * (1 if current_size > 0 else -1)
            realized_pnl_base = realized_pnl_local * fx_rate
            
            # Update position's realized P&L
            position.realized_pnl_local += realized_pnl_local
            position.realized_pnl_base += realized_pnl_base
            position.size = new_size

            # Record trade in performance metrics if tracking is enabled
            if self.track_performance:
                # trade size (should be signed of original direction)
                trade_size = abs(child_order.filled_size) * (1 if current_size > 0 else -1)
                self.trades.append(Trade(position.strategy_id, position.symbol_id,
                               position.timestamp_first_fill, child_order.timestamp_filled, position.average_price, child_order.filled_price, 
                               trade_size, realized_pnl_local, realized_pnl_base))
            return

        # Case 3: Position crosses zero (flipping from long to short or short to long)
        # Calculate realized P&L for closing the entire existing position
        realized_pnl_local = (child_order.filled_price - prev_avg_price) * current_size
        realized_pnl_base = realized_pnl_local * fx_rate
        position.realized_pnl_local += realized_pnl_local
        position.realized_pnl_base += realized_pnl_base

        # Record trade in performance metrics if tracking is enabled
        if self.track_performance:
            # trade size (should be signed of original direction)
            trade_size = abs(current_size) * (1 if current_size > 0 else -1)
            self.trades.append(Trade(position.strategy_id, position.symbol_id,
                               position.timestamp_first_fill, child_order.timestamp_filled, position.average_price, child_order.filled_price, 
                               trade_size, realized_pnl_local, realized_pnl_base))

        # Update position details based on whether it's closed completely or reversed
        if new_size == 0:
            # Position closed completely
            position.average_price = 0.0
            position.timestamp_first_fill = None
        else:
            # Position reversed (e.g., from long to short) - treat remaining size as a new position
            position.average_price = child_order.filled_price
            position.timestamp_first_fill = child_order.timestamp_filled
        position.size = new_size
        

    def get_symbol_dictionary(self) -> dict[str, Symbol]:
        """Returns the symbol dictionary."""
        return self.symbol_dict


    def get_fx_conversion_rates(self) -> dict[str, dict[date, float]]:
        """Returns the fx conversion rates."""
        return self.fx_converter.get_pricing_data()


    async def add_update_symbols(self, symbol_dict: dict[str, Symbol], validate_symbols:bool):
        """Updates the symbol dictionary."""
        await self.symbol_database.add_update_symbols(symbol_dict, validate_symbols)
        self.symbol_dict = self.symbol_database.get_symbol_dictionary()
        

    def get_current_positions(self) -> list[Position]:
        """Returns a list of all current positions."""
        return [pos for symbol_positions in self.current_positions.values() for pos in symbol_positions.values()]


    def get_orders_trades_positions(self) -> tuple[list[ParentOrderBase], list[Trade], list[Position]]:
        """Returns a tuple containing all the data needed to generate performance metrics."""
        return (self.completed_parent_orders, self.trades, self.position_records)
