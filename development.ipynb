{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9c2da4de", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "import polars as pl\n", "from datetime import datetime, timedelta\n", "from zoneinfo import ZoneInfo\n", "\n", "#config for jupyter cell output\n", "# import polars as pl\n", "# pl.Config.set_tbl_rows(20)\n", "\n", "from mattlibrary.logging.logging_config import setup_logging\n", "from mattlibrary.trading.trading_engine import TradingEngine\n", "from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI\n", "from mattlibrary.trading.symbol import Symbol, Forex, Stock\n", "from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard\n", "from mattlibrary.trading.child_order_type import ChildOrderType\n", "from mattlibrary.trading.symbol_database import SymbolDatabase\n", "from mattlibrary.trading.fx_converter import Fx_Converter\n", "\n", "#logging config\n", "setup_logging(True, \"INFO\", True, True, log_file=\"development.log\", clean_log_file=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "db3696fa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-08-25 20:12:36,020 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - __init__:27 - SimulatedExecutionEngine initialized\n", "INFO - 2025-08-25 20:12:36,089 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4559 rows) from /home/<USER>/development/python_development/trading_backend/app/data/fx_converter_data.parquet\n", "INFO - 2025-08-25 20:12:36,098 - mattlibrary.trading.symbol_database - _load_symbols_from_parquet:42 - Loaded 28 symbols from /home/<USER>/development/python_development/trading_backend/app/data/symbol_database.parquet\n", "INFO - 2025-08-25 20:12:36,099 - mattlibrary.trading.trading_engine - load_positions_from_disk:171 - 1 Positions loaded from disk: /home/<USER>/development/python_development/trading_backend/app/data/positions_client100.json\n", "INFO - 2025-08-25 20:12:36,099 - mattlibrary.trading.trading_engine - __init__:100 - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=/home/<USER>/development/python_development/trading_backend/app/data]\n"]}], "source": ["engine = TradingEngine(\n", "    client_id=\"client100\", \n", "    starting_balance=100_000, \n", "    base_currency=\"USD\", \n", "    track_performance=True, \n", "    execution_plugin_type=\"SimulatedExecutionEngine\", \n", "    static_data_directory=\"/home/<USER>/development/python_development/trading_backend/app/data\", \n", "    read_positions_from_disk=True, \n", "    write_positions_to_disk=True\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}