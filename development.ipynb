%reload_ext autoreload
%autoreload 2

import os
import sys
import polars as pl
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

#config for jupyter cell output
# import polars as pl
# pl.Config.set_tbl_rows(20)

from mattlibrary.logging.logging_config import setup_logging
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI
from mattlibrary.trading.symbol import Symbol, Forex, Stock
from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard
from mattlibrary.trading.child_order_type import ChildOrderType
from mattlibrary.trading.symbol_database import SymbolDatabase
from mattlibrary.trading.fx_converter import Fx_Converter

#logging config
setup_logging(True, "INFO", True, True, log_file="development.log", clean_log_file=True)

df = pl.read_parquet("trading_backend/app/data/fx_converter_data.parquet")
df
