"""Client instance representing a single client's trading environment.

Each ClientInstance contains all trading logic including:
- TradingEngine instance
- Interactive Brokers API connection
- Position management
- Order handling
- Market data refresh functionality
"""

import os
import logging
import hashlib
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from typing import Optional, Dict, Any, List
from datetime import date
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI
from mattlibrary.trading.parent_order_base import Parent<PERSON>rderBase
from mattlibrary.trading.position import Position
from mattlibrary.trading.symbol import Symbol
from app.configuration.settings import settings

class ClientInstance:
    """Represents a single client's trading environment with all trading functionality."""

    def __init__(self, client_id: str):
        self.client_id = client_id
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Initializing client instance: {client_id}")
        
        try:
            # Initialize trading engine
            self.trading_engine = TradingEngine(
                client_id=client_id,
                starting_balance=settings.starting_balance,
                base_currency=settings.base_currency,
                track_performance=settings.track_performance,
                execution_plugin_type=settings.execution_plugin_type,
                static_data_directory=settings.static_data_directory,
                read_positions_from_disk=settings.read_positions_from_disk,
                write_positions_to_disk=settings.write_positions_to_disk,
            )

            # Initialize Interactive Brokers API with unique client ID
            ib_client_id = int(hashlib.sha256(client_id.encode()).hexdigest(), 16) % 1000
            self.ib_api = InteractiveBrokersAPI(
                settings.ib_address,
                settings.ib_port,
                ib_client_id)
            
            self.logger.info(f"Client instance initialized successfully: {client_id} with IB client ID {ib_client_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize client instance {client_id}: {e}")
            raise


    def get_symbol_dictionary(self) -> dict[str, Symbol]:
        """Get the symbol dictionary from the symbol database."""
        self.logger.info(f"Retrieving symbol dictionary for client {self.client_id}")
        return self.trading_engine.get_symbol_dictionary()


    def get_fx_conversion_rates(self) -> dict[str, dict[date, float]]:
        """Get the fx conversion rates from the fx converter."""
        self.logger.info(f"Retrieving fx conversion rates for client {self.client_id}")
        return self.trading_engine.get_fx_conversion_rates()


    async def add_update_symbols(self, symbols: dict[str, Symbol]):
        """Add/update symbols in the symbol database."""
        self.logger.info(f"Adding/updating symbols for client {self.client_id}")
        await self.trading_engine.add_update_symbols(symbols, True)


    def get_positions(self) -> List[Position]:
        """Get current positions from the trading engine."""
        try:
            positions = self.trading_engine.get_current_positions()
            self.logger.info(f"Retrieved {len(positions)} positions for client {self.client_id}")
            return positions
        except Exception as e:
            self.logger.error(f"Error retrieving positions for client {self.client_id}: {e}")
            return []


    def submit_parent_order(self, parent_order: ParentOrderBase) -> bool:
        """Submit a parent order to the trading engine."""
        try:
            self.trading_engine.submit_parent_order(parent_order)
            self.logger.info(f"Parent order submitted for client {self.client_id}: {parent_order.strategy_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error submitting parent order for client {self.client_id}: {e}")
            return False


    async def refresh_market_data(self) -> List[Position]:
        """Refresh market data for all symbols in the trading engine."""
        try:
            # Get existing positions from trading engine
            positions = self.trading_engine.get_current_positions()
            
            # Do nothing if there are no positions
            if len(positions) == 0:
                self.logger.info(f"No positions to refresh for client {self.client_id}")
                return []
            
            # Pricing parameters          
            unique_symbol_ids = set([position.symbol_id for position in positions])
            start_dt = None
            end_dt = datetime.now(ZoneInfo("Pacific/Auckland"))
            number_ticks = 1
            what_to_show = "BID_ASK"
            use_rth_only = True
            ignore_update_size = True

            # Connect to IB API 
            await self.ib_api.connect()

            # Fetch market data update for each symbol
            symbols = self.trading_engine.get_symbol_dictionary()
            for symbol_id in unique_symbol_ids:
                symbol = symbols[symbol_id]

                # Fetch market data update
                market_data = await self.ib_api.get_historical_ticks(
                    symbol, start_dt, end_dt, number_ticks, what_to_show, use_rth_only, ignore_update_size)

                if market_data is None:
                    self.logger.warning(f"No market data received for symbol {symbol_id} for client {self.client_id}")
                    continue

                market_data = market_data[-1]

                market_data_dict = dict(
                    symbol=symbol.symbol_id,
                    datetime=market_data.timestamp,
                    bid=market_data.bid,
                    ask=market_data.ask)

                # Update trading engine with market data
                self.trading_engine.process_market_data(market_data_dict)
            
            # Save positions to disk
            self.trading_engine.save_positions_to_disk()

            # Get updated positions
            positions = self.trading_engine.get_current_positions()
            
            # Log message
            self.logger.info(f"Market data refreshed for client {self.client_id} symbols: {unique_symbol_ids}")
            
            return positions

        except Exception as e:
            self.logger.error(f"Error refreshing market data for client {self.client_id}: {e}")
            return []
        finally:
            # Disconnect
            self.ib_api.disconnect()


    async def send_websocket_message(self, message: dict):
        """Send a message to this client via the Communicator singleton."""
        # Import here to avoid circular import
        from app.communicator import communicator
        await communicator.broadcast_to_client(self.client_id, message)


    def cleanup(self):
        """Cleanup client instance resources."""
        try:
            self.logger.info(f"Cleaning up client instance: {self.client_id}")
            
            # Disconnect IB API if connected
            if hasattr(self, 'ib_api'):
                self.ib_api.disconnect()
            
            # Finalize trading engine
            if hasattr(self, 'trading_engine'):
                self.trading_engine.finalize()
                
            self.logger.info(f"Client instance cleanup completed: {self.client_id}")
        except Exception as e:
            self.logger.error(f"Error during client instance cleanup for {self.client_id}: {e}")
