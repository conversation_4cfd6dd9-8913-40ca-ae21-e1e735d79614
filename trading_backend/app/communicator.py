"""Central Communicator with FastAPI app and WebSocket handling.

The Communicator contains:
- FastAPI application instance
- All HTTP endpoints/routes
- Central WebSocket logic for all clients
- Communication models and response handling
"""
import json
import logging
import contextvars
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
from fastapi import Fast<PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Path, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from app.configuration.settings import settings
from app.models import (
    ParentOrderRequest,
    OrderSubmissionResponse,
    MarketDataRefreshResponse,
    PositionsRequestResponse,
    PositionResponse,
    ClientConnectResponse,
    ClientDisconnectResponse,
    SymbolResponse,
    SymbolsResponse,
    SaveSymbolsRequest,
    FxConversionRateResponse
)

from mattlibrary.logging.logging_config import setup_logging
from mattlibrary.trading.symbol import Symbol
from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard

# Create context variable for client_id
client_id_var = contextvars.ContextVar('client_id', default=settings.backend_id)


class ClientIdFilter(logging.Filter):
    """Custom logging filter to add client_id to log records."""
    
    def filter(self, record):
        # Get client_id from context variable
        client_id = client_id_var.get('backend')
        record.client_id = client_id
        return True


class Communicator:
    """Singleton communicator handling FastAPI app and WebSocket communications."""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Communicator, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            # Get logger
            self.logger = logging.getLogger(__name__)

            # Active WebSocket connections for clients
            self.active_websockets: Dict[str, WebSocket] = {}  # client_id -> websocket

            # Setup logging configuration FIRST
            self._setup_logging_with_client_filter()

            # get manager
            from app.manager import manager #this is imported here because the log manager should be configured first
            self.manager = manager

            # Setup FastAPI app
            self._setup_fastapi_app()

            # Mark as initialized
            Communicator._initialized = True
            self.logger.info("Backend communicator initialized successfully")
    
    def _setup_logging_with_client_filter(self):
        """Setup logging configuration with client_id filter."""
        
        # Setup single logging configuration
        setup_logging(
            logging_enabled=settings.logging_enabled,
            log_level=settings.log_level,
            log_to_console=settings.console_logging_enabled,
            log_to_file=settings.file_logging_enabled,
            log_file=settings.log_file,
            clean_log_file=settings.clean_log_file,
            log_callback=self._handle_log_message
        )
        
        # Add client_id filter to all existing handlers
        root_logger = logging.getLogger()
        client_filter = ClientIdFilter()
        
        # Apply filter to all current handlers
        for handler in root_logger.handlers:
            handler.addFilter(client_filter)
            
        # Also add filter to root logger to catch any new handlers
        root_logger.addFilter(client_filter)
    

    def _setup_fastapi_app(self):
        """Initialize FastAPI app with middleware and routes."""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            """Manage application lifespan events."""
            # Startup
            self.logger.info("Starting trading backend...listening to clients...")
            yield
            # Shutdown
            self.logger.info("Shutting down trading backend, cleaning up all clients...")
            self.manager.cleanup_all_clients()
            self.logger.info("Trading backend shutdown complete")
        
        # Create FastAPI app with lifespan
        self.app = FastAPI(
            title="Trading Backend API",
            description="Simplified trading backend with singleton architecture",
            version="1.0.0",
            lifespan=lifespan
        )
        
        # Add client_id context middleware
        @self.app.middleware("http")
        async def add_client_id_to_context(request: Request, call_next):
            """Middleware to extract client_id from URL path and set in context."""
            path_parts = request.url.path.split('/')
            
            # Extract client_id from URL patterns like /clients/{client_id}/...
            if len(path_parts) >= 3 and path_parts[1] == 'clients':
                client_id = path_parts[2]
                # Set the context variable for this request
                token = client_id_var.set(client_id)
                try:
                    response = await call_next(request)
                    return response
                finally:
                    # Reset the context variable
                    client_id_var.reset(token)
            else:
                # No client_id in path, proceed without setting context
                response = await call_next(request)
                return response
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup routes
        self._setup_routes()
    

    def _setup_routes(self):
        """Setup all HTTP and WebSocket routes."""
        
        @self.app.get("/")
        async def root():
            """Root endpoint for health check."""
            return {"message": "Trading Backend is running"}


        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {"status": "healthy", "version": "1.0.0"}
        

        @self.app.post("/clients/{client_id}/connect", response_model=ClientConnectResponse)
        async def connect(client_id: str = Path(..., description="Unique identifier for the client")) -> ClientConnectResponse:
            """Initialize a new client instance."""
            try:
                # Create client instance via manager
                self.manager.create_client_instance(client_id)
                
                return ClientConnectResponse(
                    success=True,
                    message=f"Client {client_id} initialized successfully",
                    client_id=client_id
                ) 
            
            except Exception as e:
                self.logger.error(f"Error initializing client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error initializing client: {str(e)}")
        

        @self.app.delete("/clients/{client_id}/disconnect", response_model=ClientDisconnectResponse)
        async def disconnect(client_id: str = Path(...)):
            """Remove and cleanup client instance."""
            try:
                # Remove client instance via manager
                self.manager.remove_client_instance(client_id)

                return ClientDisconnectResponse(
                    success=True,
                    message=f"Client {client_id} removed successfully",
                    client_id=client_id
                )
    
            except Exception as e:
                self.logger.error(f"Error disconnecting client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error disconnecting client: {str(e)}")


        @self.app.post("/clients/{client_id}/reconnect", response_model=ClientConnectResponse)
        async def reconnect(client_id: str = Path(..., description="Unique identifier for the client")) -> ClientConnectResponse:
            """Reconnect a client instance."""
            try:
                #Try removing existing client instance
                self.manager.remove_client_instance(client_id)

                # Create new client instance
                self.manager.create_client_instance(client_id)
                
                return ClientConnectResponse(
                    success=True,
                    message=f"Client {client_id} reconnected successfully",
                    client_id=client_id
                )
                
            except Exception as e:
                self.logger.error(f"Error reconnecting client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error reconnecting client: {str(e)}")


        @self.app.get("/clients/{client_id}/symbols", response_model=SymbolsResponse)
        async def get_symbols(client_id: str = Path(...)) -> SymbolsResponse:
            """Get symbols for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")

                # Get symbol dictionary from client instance
                symbol_dictionary = client_instance.get_symbol_dictionary()

                # Build response with symbols - convert Symbol objects to SymbolResponse objects
                symbols_response = SymbolsResponse(symbols={})

                # Iterate through symbol dictionary and convert each Symbol to SymbolResponse
                for symbol_id, symbol in symbol_dictionary.items():
                    # Create SymbolResponse with proper field mapping from Symbol object
                    symbols_response.symbols[symbol_id] = SymbolResponse(
                        symbol_id=symbol.symbol_id,
                        secType=getattr(symbol, 'secType', ''),
                        conId=getattr(symbol, 'conId', 0),
                        symbol=getattr(symbol, 'symbol', ''),
                        lastTradeDateOrContractMonth=getattr(symbol, 'lastTradeDateOrContractMonth', ''),
                        strike=getattr(symbol, 'strike', 0.0),
                        right=getattr(symbol, 'right', ''),
                        multiplier=getattr(symbol, 'multiplier', ''),
                        exchange=getattr(symbol, 'exchange', ''),
                        primaryExchange=getattr(symbol, 'primaryExchange', ''),
                        currency=getattr(symbol, 'currency', ''),
                        localSymbol=getattr(symbol, 'localSymbol', ''),
                        tradingClass=getattr(symbol, 'tradingClass', ''),
                        includeExpired=getattr(symbol, 'includeExpired', False),
                        secIdType=getattr(symbol, 'secIdType', ''),
                        secId=getattr(symbol, 'secId', ''),
                        description=getattr(symbol, 'description', ''),
                        issuerId=getattr(symbol, 'issuerId', ''),
                        contractDetails=getattr(symbol, 'contractDetails', {})
                    )

                self.logger.info(f"Retrieved {len(symbol_dictionary)} symbols for client {client_id}")
                return symbols_response

            except Exception as e:
                self.logger.error(f"Error retrieving symbols for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error retrieving symbols: {str(e)}")


        @self.app.post("/clients/{client_id}/save_symbols", response_model=SymbolsResponse)
        async def save_symbols(client_id: str = Path(...), request: SaveSymbolsRequest = Body(...)) -> SymbolsResponse:
            """Save symbols for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")

                # Extract symbols from request body and convert to Symbol objects
                symbols_dict = dict()
                for symbol_id, symbol_data in request.symbols.items():
                   
                    symbols_dict[symbol_id] = Symbol(
                        symbol_id=symbol_data.symbol_id,
                        secType=symbol_data.secType,
                        conId=symbol_data.conId,
                        symbol=symbol_data.symbol,
                        lastTradeDateOrContractMonth=symbol_data.lastTradeDateOrContractMonth,
                        strike=symbol_data.strike,
                        right=symbol_data.right,
                        multiplier=symbol_data.multiplier,
                        exchange=symbol_data.exchange,
                        primaryExchange=symbol_data.primaryExchange,
                        currency=symbol_data.currency,
                        localSymbol=symbol_data.localSymbol,
                        tradingClass=symbol_data.tradingClass,
                        includeExpired=symbol_data.includeExpired,
                        secIdType=symbol_data.secIdType,
                        secId=symbol_data.secId,
                        description=symbol_data.description,
                        issuerId=symbol_data.issuerId,
                        contractDetails=symbol_data.contractDetails
                    )

                # Add/update symbols in symbol database
                await client_instance.add_update_symbols(symbols_dict)

                # Reload symbols to confirm they were saved
                symbol_dictionary = client_instance.get_symbol_dictionary()

                # Build response with saved symbols
                symbol_responses = SymbolsResponse(symbols={})
                for symbol in symbol_dictionary.values():
                    # Create SymbolResponse with proper field mapping from Symbol object
                    symbol_responses.symbols[symbol.symbol_id] = SymbolResponse(
                        symbol_id=symbol.symbol_id,
                        secType=getattr(symbol, 'secType', ''),
                        conId=getattr(symbol, 'conId', 0),
                        symbol=getattr(symbol, 'symbol', ''),
                        lastTradeDateOrContractMonth=getattr(symbol, 'lastTradeDateOrContractMonth', ''),
                        strike=getattr(symbol, 'strike', 0.0),
                        right=getattr(symbol, 'right', ''),
                        multiplier=getattr(symbol, 'multiplier', ''),
                        exchange=getattr(symbol, 'exchange', ''),
                        primaryExchange=getattr(symbol, 'primaryExchange', ''),
                        currency=getattr(symbol, 'currency', ''),
                        localSymbol=getattr(symbol, 'localSymbol', ''),
                        tradingClass=getattr(symbol, 'tradingClass', ''),
                        includeExpired=getattr(symbol, 'includeExpired', False),
                        secIdType=getattr(symbol, 'secIdType', ''),
                        secId=getattr(symbol, 'secId', ''),
                        description=getattr(symbol, 'description', ''),
                        issuerId=getattr(symbol, 'issuerId', ''),
                        contractDetails=getattr(symbol, 'contractDetails', {})
                    )

                return symbol_responses

            except Exception as e:
                self.logger.error(f"Error saving symbols for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error saving symbols: {str(e)}")


        @self.app.get("/clients/{client_id}/fx_conversion_rates", response_model=FxConversionRateResponse)
        async def get_fx_conversion_rates(client_id: str = Path(...)) -> FxConversionRateResponse:
            """Get fx conversion rates for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")
                
                fx_conversion_rates = client_instance.get_fx_conversion_rates()
                
                return FxConversionRateResponse(conversion_rate=fx_conversion_rates)
            
            except Exception as e:
                self.logger.error(f"Error retrieving fx conversion rates for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error retrieving fx conversion rates: {str(e)}")


        @self.app.get("/clients/{client_id}/positions", response_model=PositionsRequestResponse)
        async def get_client_positions(client_id: str = Path(...)) -> PositionsRequestResponse:
            """Get positions for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")
                
                positions = client_instance.get_positions()
                
                # Convert Position objects to PositionResponse objects
                position_responses = []
                for position in positions:
                    position_responses.append(PositionResponse(
                        position_id=position.position_id,
                        strategy_id=position.strategy_id,
                        symbol_id=position.symbol_id,
                        timestamp_current=position.timestamp_current,
                        timestamp_first_fill=position.timestamp_first_fill,
                        size=position.size,
                        average_price=position.average_price,
                        current_price=position.current_price,
                        unrealized_pnl_local=position.unrealized_pnl_local,
                        unrealized_pnl_base=position.unrealized_pnl_base,
                        realized_pnl_local=position.realized_pnl_local,
                        realized_pnl_base=position.realized_pnl_base
                    ))

                self.logger.info(f"Positions requested for client {client_id}. Total positions: {len(positions)}")
                return PositionsRequestResponse(
                    success=True,
                    message=f"Positions retrieved successfully for client {client_id}. Total positions: {len(positions)}",
                    positions=position_responses
                )

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error retrieving positions for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error retrieving positions: {str(e)}")


        @self.app.post("/clients/{client_id}/orders", response_model=OrderSubmissionResponse)
        async def submit_client_order(
            order_request: ParentOrderRequest,
            client_id: str = Path(...)
        ) -> OrderSubmissionResponse:
            """Submit order for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")
                
                parent_order = ParentOrderStandard(
                    strategy_id=order_request.strategy_id,
                    symbol_id=order_request.symbol_id,
                    size=order_request.size,
                    order_type=order_request.order_type,
                    limit_price=order_request.limit_price,
                    stop_price=order_request.stop_price
                )
                
                success = client_instance.submit_parent_order(parent_order)
                return OrderSubmissionResponse(
                    success=success,
                    message="Order submitted successfully" if success else "Failed to submit order",
                    order_id=parent_order.order_id if success else None
                )
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error submitting order for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error submitting order: {str(e)}")


        @self.app.get("/clients/{client_id}/market-data/refresh", response_model=MarketDataRefreshResponse)
        async def refresh_client_market_data(client_id: str = Path(...)) -> MarketDataRefreshResponse:
            """Refresh market data for specific client."""
            try:
                client_instance = self.manager.get_client_instance(client_id)
                if not client_instance:
                    raise HTTPException(status_code=404, detail=f"Client {client_id} not found")
                
                updated_positions = await client_instance.refresh_market_data()
                
                # Convert Position objects to PositionResponse objects
                position_responses = []
                for position in updated_positions:
                    position_responses.append(PositionResponse(
                        position_id=position.position_id,
                        strategy_id=position.strategy_id,
                        symbol_id=position.symbol_id,
                        timestamp_current=position.timestamp_current,
                        timestamp_first_fill=position.timestamp_first_fill,
                        size=position.size,
                        average_price=position.average_price,
                        current_price=position.current_price,
                        unrealized_pnl_local=position.unrealized_pnl_local,
                        unrealized_pnl_base=position.unrealized_pnl_base,
                        realized_pnl_local=position.realized_pnl_local,
                        realized_pnl_base=position.realized_pnl_base
                    ))
                
                return MarketDataRefreshResponse(
                    success=True,
                    message=f"Market data refreshed for client {client_id}",
                    positions=position_responses
                )
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error refreshing market data for client {client_id}: {e}")
                raise HTTPException(status_code=500, detail=f"Error refreshing market data: {str(e)}")


        @self.app.websocket("/ws/{client_id}")
        async def central_websocket_endpoint(websocket: WebSocket, client_id: str = Path(...)):
            """Central WebSocket endpoint for all clients with client_id in path."""
            await self.open_websocket(client_id, websocket)
    

    async def open_websocket(self, client_id: str, websocket: WebSocket):
        """Open WebSocket connection for client."""
        try:
            # Accept the WebSocket connection
            await websocket.accept()

            # Store the WebSocket connection for this client
            self.active_websockets[client_id] = websocket
            self.logger.info(f"WebSocket connected for client: {client_id}")

            # Handle incoming messages
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                message_type = message.get("type")
    
                if message_type == "ping":
                    # Respond to ping with pong
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "client_id": client_id,
                        "timestamp": message.get("timestamp")
                    }))
                elif message_type == "status":
                    # Client requesting status
                    client_instance = self.manager.get_client_instance(client_id)
                    if client_instance:
                        await websocket.send_text(json.dumps({
                            "type": "status_response",
                            "client_id": client_id,
                            "status": "active",
                            "message": f"Client {client_id} is active"
                        }))
                    else:
                        await websocket.send_text(json.dumps({
                            "type": "status_response",
                            "client_id": client_id,
                            "status": "not_found",
                            "message": f"Client {client_id} not found"
                        }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "client_id": client_id,
                        "message": f"Unknown message type: {message_type}"
                    }))
    
        except WebSocketDisconnect:
            self.logger.info(f"WebSocket client disconnected: {client_id}")
        except Exception as e:
            self.logger.error(f"WebSocket error for client {client_id}: {e}")
        finally:
            # Clean up client WebSocket reference
            if client_id in self.active_websockets:
                del self.active_websockets[client_id]


    async def close_websocket(self, client_id: str):
        """Close WebSocket connection for client."""
        try:
            # Close WebSocket connection if exists and remove from active_websockets dictionary
            if client_id in self.active_websockets:
                websocket = self.active_websockets[client_id]

                try:
                    # Send close frame with appropriate code and reason
                    await websocket.close(code=1000, reason="Client removed by server")
                    self.logger.info(f"WebSocket connection closed for removed client: {client_id}")
                except Exception as e:
                    self.logger.error(f"Error closing WebSocket for client {client_id}: {e}")
                finally:
                    # Remove from dictionary after closing
                    del self.active_websockets[client_id]
            else:
                self.logger.info(f"No WebSocket connection found for client {client_id}")

        except Exception as e:
            self.logger.error(f"Error closing WebSocket for client {client_id}: {e}")
           

    async def broadcast_to_client(self, client_id: str, message: dict):
        """Send a message to a specific client via WebSocket."""
        if client_id in self.active_websockets:
            try:
                websocket = self.active_websockets[client_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                print(f"Error broadcasting message to client {client_id}: {e} : message: {message}")


    def _handle_log_message(self, log_record: logging.LogRecord):
        """Handle log messages and broadcast to WebSocket clients."""
        
        # Extract client_id from the log record (set by ClientIdFilter)
        if hasattr(log_record, 'client_id'):
            
            #capture client_id
            client_id = log_record.client_id

            # Create log data message
            log_data = {
                "type": "log",
                "client_id": client_id,
                "level": log_record.levelname,
                "logger_name": log_record.name,
                "message": log_record.message,
                "timestamp": log_record.created,
                "module": log_record.module,
                "function": log_record.funcName,
                "line": log_record.lineno
            }

            if client_id != 'backend':
                #send log message to specific client

                #check whether websocket exists for client
                if client_id in self.active_websockets:
                    # Schedule the broadcast (since this is called from sync context)
                    try:
                        asyncio.create_task(self.broadcast_to_client(client_id, log_data))
                    except RuntimeError as e:
                        print(f"Error broadcasting log message for client {client_id} - Error Message: {e}")
                else:
                    print(f"No websocket connection found for client {client_id}")
            else:
                #send log message to all clients
                for client_id in self.active_websockets:
                    try:
                        asyncio.create_task(self.broadcast_to_client(client_id, log_data))
                    except RuntimeError as e:
                        print(f"Error broadcasting log message via Websocket to client {client_id} - Error Message: {e}")              
        else:
            print("Tried to send log message to clients via Websocket but no client_id found in log record.")


# Global singleton instance
communicator = Communicator()
