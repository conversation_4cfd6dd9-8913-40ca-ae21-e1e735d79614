"""FastAPI main application entry point for trading backend.

This module initializes the singleton Manager and Communicator instances
and sets up the FastAPI application with simplified architecture.
"""
def create_app():
    """Create and configure the FastAPI application."""
    # Return the FastAPI app from communicator (with lifespan already configured)
    return communicator.app

# Create the app
from app.communicator import communicator
app = communicator.app