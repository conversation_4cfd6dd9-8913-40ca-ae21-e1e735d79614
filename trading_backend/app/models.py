"""Pydantic models for the simplified Manual Trading API.

This consolidates request and response models into a single place
so the API code stays compact and readable.
"""
from __future__ import annotations
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
from mattlibrary.trading.child_order_type import ChildOrderType
from mattlibrary.trading.symbol import Symbol


class GetSymbolsRequest(BaseModel):
    """Request model for retrieving symbols."""
    

class SaveSymbolsRequest(BaseModel):
    """Request model for saving symbols."""
    symbols: Dict[str, SymbolResponse]  # Dictionary with symbol_id as key, symbol data as value


class ParentOrderRequest(BaseModel):
    """Request model for submitting parent orders."""
    strategy_id: str
    symbol_id: str
    size: int
    order_type: ChildOrderType
    limit_price: Optional[float]
    stop_price: Optional[float]
    custom_datetime: Optional[str]


class MarketDataRefreshRequest(BaseModel):
    """Request model for market data refresh."""
    symbols: Optional[list[str]]


class SymbolResponse(BaseModel):
    """Response model for symbol data based on IB contract specifications."""
    symbol_id: str # Unique identifier for the symbol
    secType: str # Security type (STK, FUT, OPT, etc.)
    conId: int # The unique IB contract identifier
    symbol: str # The contract (or its underlying) symbol
    lastTradeDateOrContractMonth: str # YYYYMM or YYYYMMDD format
    strike: float # Strike price for options
    right: str # Put or call (only for options)
    multiplier: str # Contract multiplier
    exchange: str # Destination exchange
    primaryExchange: str # Primary exchange
    currency: str # Underlying currency
    localSymbol: str # Local symbol of the underlying asset
    tradingClass: str # Trading class name
    includeExpired: bool # Include expired contracts
    secIdType: str # Security identifier type
    secId: str # Security identifier
    description: str # Description for this symbol
    issuerId: str # The issuer's ID
    contractDetails: Dict[str, Any] # Dictionary containing contract details


class SymbolsResponse(BaseModel):
    """Response model for collection of symbols."""
    symbols: Dict[str, SymbolResponse]


class FxConversionRateResponse(BaseModel):
    """Response model for FX conversion rate."""
    conversion_rate: dict[str, dict[str, float]]


class PositionResponse(BaseModel):
    """Response model for position data."""
    position_id: str
    strategy_id: str
    symbol_id: str
    timestamp_current: datetime
    timestamp_first_fill: datetime
    size: int
    average_price: float
    current_price: float
    unrealized_pnl_local: float
    unrealized_pnl_base: float
    realized_pnl_local: float
    realized_pnl_base: float


class PositionsResponse(BaseModel):
    """Response model for collection of positions."""
    positions: List[PositionResponse]


class OrderSubmissionResponse(BaseModel):
    """Response model for order submission."""
    success: bool
    message: str
    order_id: str


class PositionsRequestResponse(BaseModel):
    """Response model for positions request."""
    success: bool
    message: str
    positions: List[PositionResponse]


class MarketDataRefreshResponse(BaseModel):
    """Response model for market data refresh."""
    success: bool
    message: str
    positions: List[PositionResponse]


class ClientConnectResponse(BaseModel):
    """Response model for client initialization."""
    success: bool
    message: str
    client_id: str


class ClientDisconnectResponse(BaseModel):
    """Response model for client removal."""
    success: bool
    message: str
    client_id: str

