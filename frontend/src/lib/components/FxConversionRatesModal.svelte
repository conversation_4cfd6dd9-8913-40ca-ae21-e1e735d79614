<script lang="ts">
  import { fxConversionRatesStore, forceLoadFxConversionRates } from '$lib/stores/fxConversionRates';

  // Props
  let { isOpen = $bindable(false) } = $props();

  // Local state
  let isLoading = $state(false);
  let errorMessage = $state('');
  let successMessage = $state('');

  // Reactive data processing
  let processedData = $derived.by(() => {
    const rates = $fxConversionRatesStore.conversionRates;

    if (!rates || Object.keys(rates).length === 0) {
      return { dates: [], currencyPairs: [], gridData: [] };
    }

    // Get all unique dates from all currency pairs
    const allDates = new Set<string>();
    Object.values(rates).forEach(dateRates => {
      Object.keys(dateRates).forEach(date => allDates.add(date));
    });

    // Sort dates chronologically
    const sortedDates = Array.from(allDates).sort();

    // Get currency pairs
    const currencyPairs = Object.keys(rates).sort();

    // Create grid data: array of rows, each row has date + values for each currency pair
    const gridData = sortedDates.map(date => {
      const row: Record<string, string | number> = { date };
      currencyPairs.forEach(pair => {
        row[pair] = rates[pair][date] || '';
      });
      return row;
    });

    return { dates: sortedDates, currencyPairs, gridData };
  });

  // Close modal
  function closeModal() {
    isOpen = false;
    errorMessage = '';
    successMessage = '';
  }

  // Reload FX conversion rates from backend
  async function reloadFxConversionRates() {
    isLoading = true;
    errorMessage = '';
    successMessage = '';

    try {
      // Force fetch fresh FX conversion rates from backend
      await forceLoadFxConversionRates();
      successMessage = 'FX conversion rates reloaded successfully';
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : 'Failed to reload FX conversion rates';
    } finally {
      isLoading = false;
    }
  }

  // Handle escape key to close modal
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeModal();
    }
  }
</script>

<!-- Modal backdrop and content -->
{#if isOpen}
  <div class="modal-backdrop" onclick={closeModal} onkeydown={handleKeydown} role="dialog" aria-modal="true" tabindex="-1">
    <div class="modal-content" onclick={(e) => e.stopPropagation()} role="document">
      <div class="modal-header">
        <h2>FX Conversion Rates</h2>
        <button class="close-button" onclick={closeModal} aria-label="Close modal">
          ×
        </button>
      </div>

      <div class="modal-body">
        <!-- Action buttons card -->
        <div class="actions-card">
          <div class="action-buttons">
            <button class="btn-action" onclick={reloadFxConversionRates} disabled={isLoading}>
              {isLoading ? 'Loading...' : 'Reload FX Rates'}
            </button>
          </div>
        </div>

        <!-- Status messages -->
        {#if errorMessage}
          <div class="error-message">
            {errorMessage}
          </div>
        {/if}

        {#if successMessage}
          <div class="success-message">
            {successMessage}
          </div>
        {/if}

        <!-- FX Conversion Rates grid -->
        <div class="grid-container">
          <div class="grid-wrapper">
            {#if processedData.gridData.length === 0}
              <div class="no-data">No FX conversion rates available</div>
            {:else}
              <table class="fx-rates-table">
                <thead>
                  <tr>
                    <th class="date-col">Date</th>
                    {#each processedData.currencyPairs as pair}
                      <th class="rate-col">{pair}</th>
                    {/each}
                  </tr>
                </thead>
                <tbody>
                  {#each processedData.gridData as row}
                    <tr>
                      <td class="date-cell">{row.date}</td>
                      {#each processedData.currencyPairs as pair}
                        <td class="rate-cell">
                          {typeof row[pair] === 'number' ? row[pair].toFixed(6) : row[pair]}
                        </td>
                      {/each}
                    </tr>
                  {/each}
                </tbody>
              </table>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Modal styles - matching SymbolModal */
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--surface);
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 98vw;
    width: 1800px;
    height: 85vh;
    overflow: hidden;
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    flex-shrink: 0;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #f97316; /* Orange color matching other modals */
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    background: var(--hover);
    color: var(--text);
  }

  .modal-body {
    padding: 1.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
  }

  /* Actions card */
  .actions-card {
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-action {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--surface);
    color: var(--text);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-action:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: #f97316;
  }

  .btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Status messages */
  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1.25rem;
    font-size: 0.875rem;
  }

  .success-message {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1.25rem;
    font-size: 0.875rem;
  }

  /* Grid container */
  .grid-container {
    flex: 1;
    min-height: 600px;
    max-height: 700px;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .grid-wrapper {
    flex: 1;
    overflow: auto;
    min-height: 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Table styles */
  .fx-rates-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: auto;
    will-change: scroll-position;
    transform: translateZ(0);
  }

  .fx-rates-table th,
  .fx-rates-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
    white-space: nowrap;
  }

  .fx-rates-table th {
    background: var(--surface-2);
    font-weight: 600;
    color: var(--text);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--border);
  }

  .date-col {
    min-width: 120px;
    position: sticky;
    left: 0;
    background: var(--surface-2);
    z-index: 11;
  }

  .rate-col {
    min-width: 100px;
  }

  .date-cell {
    font-weight: 600;
    background: var(--surface);
    position: sticky;
    left: 0;
    z-index: 5;
  }

  .rate-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
  }

  .fx-rates-table tbody tr:hover {
    background: var(--hover);
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--muted);
    font-size: 1rem;
  }
</style>
