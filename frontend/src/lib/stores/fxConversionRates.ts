import { writable, get } from 'svelte/store';
import { fetchFxConversionRates } from '$lib/api';
import { clientStore } from './client';

// Export a single store object (typed)
export interface FxConversionRatesState {
  conversionRates: Record<string, Record<string, number>>; // dict[str, dict[date, float]]
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}

export const fxConversionRatesStore = writable<FxConversionRatesState>({
  conversionRates: {},
  isLoading: false,
  error: null,
  isInitialized: false, // Internal flag
});

/**
 * Load FX conversion rates from the backend API
 * Only fetches if not already initialized to avoid unnecessary requests
 */
export async function loadFxConversionRates() {
  if (import.meta.env.DEV) console.log('loadFxConversionRates() called');

  // Don't fetch if no client ID is available
  const client = get(clientStore);
  if (!client.clientId) {
    if (import.meta.env.DEV) console.log('loadFxConversionRates: No client ID configured');
    fxConversionRatesStore.update(store => ({
      ...store,
      conversionRates: {},
      error: 'No client ID configured'
    }));
    return;
  }

  // Only fetch if it's not already initialized
  const currentStore = get(fxConversionRatesStore);
  if (currentStore.isInitialized) {
    if (import.meta.env.DEV) console.log('loadFxConversionRates: FX conversion rates already initialized, skipping fetch');
    return;
  }

  if (import.meta.env.DEV) console.log('loadFxConversionRates: Starting fetch for client:', client.clientId);
  fxConversionRatesStore.update(store => ({
    ...store,
    isLoading: true,
    error: null
  }));

  try {
    if (import.meta.env.DEV) console.log('Fetching FX conversion rates...');
    const response = await fetchFxConversionRates();
    if (import.meta.env.DEV) console.log('FX conversion rates API response:', response);

    fxConversionRatesStore.update(store => ({
      ...store,
      conversionRates: { ...response.conversion_rate },
      isInitialized: true,
      isLoading: false
    }));

    if (import.meta.env.DEV) console.log('Updated FX conversion rates:', Object.keys(response.conversion_rate).length, 'currency pairs');
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
    console.error('loadFxConversionRates error:', errorMessage);
    fxConversionRatesStore.update(store => ({
      ...store,
      error: errorMessage,
      isLoading: false
    }));
  }
}

/**
 * Force load FX conversion rates from backend (bypasses initialization check)
 * Useful for manual refresh operations
 */
export async function forceLoadFxConversionRates() {
  if (import.meta.env.DEV) console.log('forceLoadFxConversionRates() called - forcing fresh fetch from backend');

  // Don't fetch if no client ID is available
  const client = get(clientStore);
  if (!client.clientId) {
    if (import.meta.env.DEV) console.log('forceLoadFxConversionRates: No client ID configured');
    fxConversionRatesStore.update(store => ({
      ...store,
      conversionRates: {},
      error: 'No client ID configured'
    }));
    return;
  }

  if (import.meta.env.DEV) console.log('forceLoadFxConversionRates: Starting forced fetch for client:', client.clientId);
  fxConversionRatesStore.update(store => ({
    ...store,
    isLoading: true,
    error: null
  }));

  try {
    if (import.meta.env.DEV) console.log('Force fetching FX conversion rates from backend...');
    const response = await fetchFxConversionRates();
    if (import.meta.env.DEV) console.log('FX conversion rates API response:', response);

    fxConversionRatesStore.update(store => ({
      ...store,
      conversionRates: { ...response.conversion_rate },
      isInitialized: true,
      isLoading: false
    }));

    if (import.meta.env.DEV) console.log('Force updated FX conversion rates:', Object.keys(response.conversion_rate).length, 'currency pairs');
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
    console.error('forceLoadFxConversionRates error:', errorMessage);
    fxConversionRatesStore.update(store => ({
      ...store,
      error: errorMessage,
      isLoading: false
    }));
  }
}

/**
 * Reset FX conversion rates when disconnecting
 * Clears all data and resets initialization state
 */
export function resetFxConversionRates() {
  if (import.meta.env.DEV) console.log('resetFxConversionRates() called');
  fxConversionRatesStore.update(store => ({
    ...store,
    conversionRates: {},
    isInitialized: false,
    error: null,
    isLoading: false
  }));
}
