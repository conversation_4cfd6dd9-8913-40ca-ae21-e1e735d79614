export interface Position {
  position_id: string;
  strategy_id: string;
  symbol_id: string;
  timestamp_current: string;
  timestamp_first_fill: string;
  size: number;
  average_price: number;
  current_price: number;
  unrealized_pnl_local: number;
  unrealized_pnl_base: number;
  realized_pnl_local: number;
  realized_pnl_base: number;
}

export interface PositionsApiResponse {
  success: boolean;
  message: string;
  positions: Position[];
}

export interface OrderRequest {
    strategy_id: string;
    symbol_id: string;
    size: number;
    order_type: string;
    limit_price: number;
    stop_price: number;
    custom_datetime: string;
}

export interface OrderSubmissionResponse {
  success: boolean;
  message: string;
  order_id: string;
}

export interface MarketDataRefreshResponse extends PositionsApiResponse {}

export interface ClientConnectResponse {
  success: boolean;
  message: string;
  client_id: string;
}

export interface ClientDisconnectResponse {
  success: boolean;
  message: string;
  client_id: string;
}

export interface Symbol {
  symbol_id: string; // Unique identifier for the symbol
  secType: string; // Security type (STK, FUT, OPT, etc.)
  conId: number; // The unique IB contract identifier
  symbol: string; // The contract (or its underlying) symbol
  lastTradeDateOrContractMonth: string; // YYYYMM or YYYYMMDD format
  strike: number; // Strike price for options
  right: string; // Put or call (only for options)
  multiplier: string; // Contract multiplier
  exchange: string; // Destination exchange
  primaryExchange: string; // Primary exchange
  currency: string; // Underlying currency
  localSymbol: string; // Local symbol of the underlying asset
  tradingClass: string; // Trading class name
  includeExpired: boolean; // Include expired contracts
  secIdType: string; // Security identifier type
  secId: string; // Security identifier
  description: string; // Description for this symbol
  issuerId: string; // The issuer's ID
  contractDetails: Record<string, string>; // Dictionary containing contract details
}

export interface GetSymbolsApiResponse {
  symbols: Record<string, Symbol>;
}

export interface SaveSymbolsRequest {
  symbols: Record<string, Symbol>; // Dictionary with symbolId as key
}

// Response interface for save symbols operation  
export interface SaveSymbolsResponse {
  symbols: Record<string, Symbol>; // Dictionary with symbolId as key
}

export interface LogMessage {
  timestamp: string; // ISO format, e.g., "2025-08-15T18:48:14.123"
  level: string;     // e.g., "INFO", "WARNING", "ERROR"
  source: string;    // Corresponds to the Python logger's 'name'
  message: string;   // The actual log message content
}
